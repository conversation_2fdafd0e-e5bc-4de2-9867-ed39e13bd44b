<template>
  <div class="patent-upload-view">
    <div class="container-fluid py-4">
      <div class="row justify-content-center">
        <div class="col-lg-8">
          <!-- Page Header -->
          <div class="d-flex align-items-center mb-4">
            <i class="bi bi-upload text-primary me-3" style="font-size: 2rem;"></i>
            <div>
              <h2 class="mb-1">专利上传</h2>
              <p class="text-muted mb-0">上传您的专利信息和相关文档</p>
            </div>
          </div>

          <!-- Upload Form -->
          <div class="card shadow-sm">
            <div class="card-body p-4">
              <form @submit.prevent="submitPatent">
                <!-- Basic Information -->
                <div class="row mb-4">
                  <div class="col-12">
                    <h5 class="text-primary mb-3">
                      <i class="bi bi-info-circle me-2"></i>
                      基本信息
                    </h5>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label for="patentName" class="form-label">专利名称 *</label>
                    <input
                      type="text"
                      class="form-control"
                      id="patentName"
                      v-model="form.patentName"
                      required
                      placeholder="请输入专利名称"
                    >
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label for="patentNumber" class="form-label">专利号 *</label>
                    <input
                      type="text"
                      class="form-control"
                      id="patentNumber"
                      v-model="form.patentNumber"
                      required
                      placeholder="请输入专利号"
                    >
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label for="patentCategory" class="form-label">专利类别 *</label>
                    <select
                      class="form-select"
                      id="patentCategory"
                      v-model="form.patentCategory"
                      required
                    >
                      <option value="">请选择专利类别</option>
                      <option value="invention">发明专利</option>
                      <option value="utility">实用新型专利</option>
                      <option value="design">外观设计专利</option>
                    </select>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label for="transferPrice" class="form-label">转让价格 (ETH) *</label>
                    <input
                      type="number"
                      step="0.001"
                      class="form-control"
                      id="transferPrice"
                      v-model="form.transferPrice"
                      required
                      placeholder="0.000"
                    >
                  </div>
                  
                  <div class="col-12 mb-3">
                    <label for="patentAbstract" class="form-label">专利摘要 *</label>
                    <textarea
                      class="form-control"
                      id="patentAbstract"
                      rows="4"
                      v-model="form.patentAbstract"
                      required
                      placeholder="请输入专利摘要"
                    ></textarea>
                  </div>
                </div>

                <!-- Date Information -->
                <div class="row mb-4">
                  <div class="col-12">
                    <h5 class="text-primary mb-3">
                      <i class="bi bi-calendar me-2"></i>
                      日期信息
                    </h5>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label for="applicationDate" class="form-label">专利申请日期 *</label>
                    <input
                      type="date"
                      class="form-control"
                      id="applicationDate"
                      v-model="form.applicationDate"
                      required
                    >
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label for="expirationDate" class="form-label">专利权结束日期 *</label>
                    <input
                      type="date"
                      class="form-control"
                      id="expirationDate"
                      v-model="form.expirationDate"
                      required
                    >
                  </div>
                </div>

                <!-- Owner Information -->
                <div class="row mb-4">
                  <div class="col-12">
                    <h5 class="text-primary mb-3">
                      <i class="bi bi-person me-2"></i>
                      专利权人信息
                    </h5>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label for="ownerName" class="form-label">专利权人姓名 *</label>
                    <input
                      type="text"
                      class="form-control"
                      id="ownerName"
                      v-model="form.ownerName"
                      required
                      placeholder="请输入专利权人姓名"
                    >
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label for="ownerIdNumber" class="form-label">专利权人身份证号 *</label>
                    <input
                      type="text"
                      class="form-control"
                      id="ownerIdNumber"
                      v-model="form.ownerIdNumber"
                      required
                      placeholder="请输入身份证号"
                    >
                  </div>
                </div>

                <!-- Agency Information -->
                <div class="row mb-4">
                  <div class="col-12">
                    <h5 class="text-primary mb-3">
                      <i class="bi bi-building me-2"></i>
                      代理信息
                    </h5>
                  </div>
                  
                  <div class="col-12 mb-3">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        id="isAgentSale"
                        v-model="form.isAgentSale"
                      >
                      <label class="form-check-label" for="isAgentSale">
                        是否为代理出售
                      </label>
                    </div>
                  </div>
                </div>

                <!-- Document Upload -->
                <div class="row mb-4">
                  <div class="col-12">
                    <h5 class="text-primary mb-3">
                      <i class="bi bi-file-earmark-arrow-up me-2"></i>
                      文档上传
                    </h5>
                  </div>
                  
                  <div class="col-12 mb-3">
                    <label for="documentFile" class="form-label">
                      {{ form.isAgentSale ? '专利代理委托证明文档' : '专利权证明文档' }} *
                    </label>
                    <input
                      type="file"
                      class="form-control"
                      id="documentFile"
                      @change="handleFileChange"
                      accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                      required
                    >
                    <div class="form-text">
                      支持格式：PDF, DOC, DOCX, JPG, JPEG, PNG，最大文件大小：10MB
                    </div>
                  </div>
                  
                  <div v-if="selectedFile" class="col-12 mb-3">
                    <div class="alert alert-info">
                      <i class="bi bi-file-earmark me-2"></i>
                      已选择文件：{{ selectedFile.name }} ({{ formatFileSize(selectedFile.size) }})
                    </div>
                  </div>
                </div>

                <!-- Submit Buttons -->
                <div class="row">
                  <div class="col-12">
                    <div class="d-flex gap-3">
                      <button
                        type="submit"
                        class="btn btn-primary"
                        :disabled="isSubmitting"
                      >
                        <span v-if="isSubmitting" class="spinner-border spinner-border-sm me-2"></span>
                        <i v-else class="bi bi-upload me-2"></i>
                        {{ isSubmitting ? '上传中...' : '提交专利' }}
                      </button>
                      
                      <button
                        type="button"
                        class="btn btn-outline-secondary"
                        @click="resetForm"
                        :disabled="isSubmitting"
                      >
                        <i class="bi bi-arrow-clockwise me-2"></i>
                        重置表单
                      </button>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useAuthStore } from '@/stores/auth'

export default {
  name: 'PatentUploadView',
  setup() {
    const authStore = useAuthStore()
    
    const form = reactive({
      patentName: '',
      patentNumber: '',
      patentCategory: '',
      transferPrice: '',
      patentAbstract: '',
      applicationDate: '',
      expirationDate: '',
      ownerName: '',
      ownerIdNumber: '',
      isAgentSale: false
    })
    
    const selectedFile = ref(null)
    const isSubmitting = ref(false)

    const handleFileChange = (event) => {
      const file = event.target.files[0]
      if (file) {
        // Validate file size (10MB max)
        if (file.size > 10 * 1024 * 1024) {
          alert('文件大小不能超过10MB')
          event.target.value = ''
          return
        }
        selectedFile.value = file
      }
    }

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const submitPatent = async () => {
      try {
        isSubmitting.value = true
        
        // Validate form
        if (!selectedFile.value) {
          alert('请选择要上传的文档')
          return
        }

        // Here you would:
        // 1. Upload file to IPFS
        // 2. Create patent record on blockchain
        // 3. Handle the response
        
        console.log('Submitting patent:', form)
        console.log('File:', selectedFile.value)
        
        // Mock submission delay
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        alert('专利上传成功！等待审核中...')
        resetForm()
        
      } catch (error) {
        console.error('Patent submission failed:', error)
        alert('专利上传失败，请重试')
      } finally {
        isSubmitting.value = false
      }
    }

    const resetForm = () => {
      Object.keys(form).forEach(key => {
        if (typeof form[key] === 'boolean') {
          form[key] = false
        } else {
          form[key] = ''
        }
      })
      selectedFile.value = null
      document.getElementById('documentFile').value = ''
    }

    return {
      authStore,
      form,
      selectedFile,
      isSubmitting,
      handleFileChange,
      formatFileSize,
      submitPatent,
      resetForm
    }
  }
}
</script>

<style scoped>
.patent-upload-view {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.card {
  border: none;
  border-radius: 0.75rem;
}

.form-label {
  font-weight: 600;
  color: #495057;
}

.form-control:focus,
.form-select:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.text-primary {
  color: #0d6efd !important;
}

.alert-info {
  background-color: #e7f3ff;
  border-color: #b8daff;
  color: #0c5460;
}
</style>
