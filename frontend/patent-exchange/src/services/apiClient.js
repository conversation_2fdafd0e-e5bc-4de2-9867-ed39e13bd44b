import axios from 'axios'

// API configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api'
const IPFS_BASE_URL = import.meta.env.VITE_IPFS_BASE_URL || 'http://localhost:5001/api/v0'

// Create axios instance for API calls
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds timeout
  headers: {
    'Content-Type': 'application/json'
  }
})

// Create axios instance for IPFS calls
const ipfsClient = axios.create({
  baseURL: IPFS_BASE_URL,
  timeout: 60000, // 60 seconds timeout for file uploads
})

// Request interceptor to add authentication headers
apiClient.interceptors.request.use(
  (config) => {
    // Get current user address from localStorage or auth store
    const userAddress = localStorage.getItem('userAddress')
    if (userAddress) {
      config.headers['X-User-Address'] = userAddress
    }
    
    // Add timestamp for request tracking
    config.metadata = { startTime: new Date() }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling and logging
apiClient.interceptors.response.use(
  (response) => {
    // Log successful requests in development
    if (import.meta.env.DEV) {
      const duration = new Date() - response.config.metadata.startTime
      console.log(`✅ API Success: ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`)
    }
    
    return response
  },
  (error) => {
    // Log errors in development
    if (import.meta.env.DEV) {
      const duration = error.config?.metadata ? new Date() - error.config.metadata.startTime : 0
      console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url} (${duration}ms)`, error.response?.data || error.message)
    }
    
    // Handle specific error cases
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // Unauthorized - redirect to login or refresh auth
          console.warn('Unauthorized access - user may need to reconnect wallet')
          break
        case 403:
          // Forbidden - insufficient permissions
          console.warn('Insufficient permissions for this operation')
          break
        case 404:
          // Not found
          console.warn('Requested resource not found')
          break
        case 429:
          // Rate limited
          console.warn('Rate limit exceeded - please try again later')
          break
        case 500:
          // Server error
          console.error('Internal server error')
          break
      }
      
      // Return standardized error format
      const apiError = new Error(data?.error?.message || `HTTP ${status} Error`)
      apiError.code = data?.error?.code || `HTTP_${status}`
      apiError.status = status
      apiError.details = data?.error?.details
      
      return Promise.reject(apiError)
    } else if (error.request) {
      // Network error
      const networkError = new Error('Network error - please check your connection')
      networkError.code = 'NETWORK_ERROR'
      return Promise.reject(networkError)
    } else {
      // Other error
      return Promise.reject(error)
    }
  }
)

// IPFS request interceptor
ipfsClient.interceptors.request.use(
  (config) => {
    config.metadata = { startTime: new Date() }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// IPFS response interceptor
ipfsClient.interceptors.response.use(
  (response) => {
    if (import.meta.env.DEV) {
      const duration = new Date() - response.config.metadata.startTime
      console.log(`✅ IPFS Success: ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`)
    }
    return response
  },
  (error) => {
    if (import.meta.env.DEV) {
      const duration = error.config?.metadata ? new Date() - error.config.metadata.startTime : 0
      console.error(`❌ IPFS Error: ${error.config?.method?.toUpperCase()} ${error.config?.url} (${duration}ms)`, error.response?.data || error.message)
    }
    
    const ipfsError = new Error(error.response?.data?.message || 'IPFS operation failed')
    ipfsError.code = 'IPFS_ERROR'
    return Promise.reject(ipfsError)
  }
)

// API helper functions
export const api = {
  // User endpoints
  user: {
    getRole: (address) => apiClient.get(`/user/role/${address}`),
    updateRole: (data) => apiClient.post('/user/role', data),
    getProfile: (address) => apiClient.get(`/user/profile/${address}`),
    updateProfile: (address, data) => apiClient.put(`/user/profile/${address}`, data),
    getAll: (params) => apiClient.get('/admin/users', { params }),
    getStatistics: () => apiClient.get('/admin/users/statistics')
  },

  // Patent endpoints
  patents: {
    upload: (formData) => apiClient.post('/patents/upload', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),
    search: (params) => apiClient.get('/patents/search', { params }),
    getById: (id) => apiClient.get(`/patents/${id}`),
    getUserPatents: (address) => apiClient.get(`/patents/user/${address}`),
    withdraw: (id, data) => apiClient.put(`/patents/${id}/withdraw`, data),
    restore: (id) => apiClient.put(`/patents/${id}/restore`),
    download: (id, documentType) => apiClient.get(`/patents/${id}/download/${documentType}`, {
      responseType: 'blob'
    })
  },

  // Transaction endpoints
  transactions: {
    initiate: (data) => apiClient.post('/transactions/initiate', data),
    getUserTransactions: (address, params) => apiClient.get(`/transactions/user/${address}`, { params }),
    getPending: () => apiClient.get('/transactions/pending'),
    approve: (id, data) => apiClient.put(`/transactions/${id}/approve`, data),
    reject: (id, data) => apiClient.put(`/transactions/${id}/reject`, data)
  },

  // Review endpoints
  review: {
    getPendingUploads: () => apiClient.get('/review/uploads/pending'),
    approveUpload: (id, data) => apiClient.put(`/review/uploads/${id}/approve`, data),
    rejectUpload: (id, data) => apiClient.put(`/review/uploads/${id}/reject`, data)
  },

  // Protection endpoints
  protection: {
    request: (data) => apiClient.post('/protection/request', data),
    getPending: () => apiClient.get('/protection/pending'),
    getCases: () => apiClient.get('/protection/cases/pending'),
    approve: (id, data) => apiClient.put(`/protection/${id}/approve`, data),
    reject: (id, data) => apiClient.put(`/protection/${id}/reject`, data)
  },

  // Notification endpoints
  notifications: {
    getByAddress: (address, params) => apiClient.get(`/notifications/${address}`, { params }),
    markAsRead: (id) => apiClient.put(`/notifications/${id}/read`),
    markAllAsRead: (address) => apiClient.put(`/notifications/${address}/read-all`),
    send: (data) => apiClient.post('/notifications/send', data)
  },

  // Admin endpoints
  admin: {
    getOverviewStats: () => apiClient.get('/admin/statistics/overview'),
    getPatentStats: (params) => apiClient.get('/admin/statistics/patents', { params }),
    getTransactionStats: () => apiClient.get('/admin/statistics/transactions')
  },

  // IPFS endpoints
  ipfs: {
    upload: (formData) => apiClient.post('/ipfs/upload', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  }
}

// IPFS helper functions
export const ipfs = {
  upload: (files) => ipfsClient.post('/add', files, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  get: (hash) => ipfsClient.get(`/cat?arg=${hash}`),
  pin: (hash) => ipfsClient.post(`/pin/add?arg=${hash}`)
}

// Export clients for direct use if needed
export { apiClient, ipfsClient }

// Default export
export default api
