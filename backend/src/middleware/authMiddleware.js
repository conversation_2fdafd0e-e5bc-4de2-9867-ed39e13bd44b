const blockchainService = require('../services/blockchainService');

/**
 * Authentication middleware for verifying user addresses and roles
 */

// Extract user address from request headers
const extractUserAddress = (req, res, next) => {
  try {
    const userAddress = req.headers['x-user-address'] || req.body.userAddress || req.query.userAddress;
    
    if (!userAddress) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'AUTHENTICATION_ERROR',
          message: 'User address is required'
        }
      });
    }

    // Validate address format
    if (!blockchainService.isValidAddress(userAddress)) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'AUTHENTICATION_ERROR',
          message: 'Invalid user address format'
        }
      });
    }

    // Convert to checksum address
    req.userAddress = blockchainService.toChecksumAddress(userAddress);
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Authentication failed'
      }
    });
  }
};

// Verify user exists and is active
const verifyActiveUser = async (req, res, next) => {
  try {
    if (!req.userAddress) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'AUTHENTICATION_ERROR',
          message: 'User address not found in request'
        }
      });
    }

    // Check if user is active in the blockchain
    const isActive = await blockchainService.callContractMethod(
      'UserManagement',
      'isUserActive',
      [req.userAddress]
    );

    if (!isActive) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'AUTHORIZATION_ERROR',
          message: 'User account is not active'
        }
      });
    }

    next();
  } catch (error) {
    console.error('User verification error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'User verification failed'
      }
    });
  }
};

// Get user role from blockchain
const getUserRole = async (req, res, next) => {
  try {
    if (!req.userAddress) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'AUTHENTICATION_ERROR',
          message: 'User address not found in request'
        }
      });
    }

    // Get user role from blockchain
    const role = await blockchainService.callContractMethod(
      'UserManagement',
      'getUserRole',
      [req.userAddress]
    );

    // Convert role number to string
    const roleMap = {
      0: 'user',
      1: 'reviewer', 
      2: 'admin'
    };

    req.userRole = roleMap[role] || 'user';
    next();
  } catch (error) {
    console.error('Role retrieval error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Role retrieval failed'
      }
    });
  }
};

// Require specific role
const requireRole = (requiredRoles) => {
  return (req, res, next) => {
    if (!req.userRole) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'AUTHENTICATION_ERROR',
          message: 'User role not determined'
        }
      });
    }

    const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
    
    if (!roles.includes(req.userRole)) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'AUTHORIZATION_ERROR',
          message: `Access denied. Required role: ${roles.join(' or ')}, current role: ${req.userRole}`
        }
      });
    }

    next();
  };
};

// Require admin role
const requireAdmin = requireRole(['admin']);

// Require reviewer or admin role
const requireReviewerOrAdmin = requireRole(['reviewer', 'admin']);

// Optional authentication (doesn't fail if no user address)
const optionalAuth = (req, res, next) => {
  const userAddress = req.headers['x-user-address'] || req.body.userAddress || req.query.userAddress;
  
  if (userAddress && blockchainService.isValidAddress(userAddress)) {
    req.userAddress = blockchainService.toChecksumAddress(userAddress);
  }
  
  next();
};

// Middleware to update last login time
const updateLastLogin = async (req, res, next) => {
  try {
    if (req.userAddress) {
      // Update last login time in blockchain (fire and forget)
      blockchainService.callContractMethod(
        'UserManagement',
        'updateLastLogin',
        [req.userAddress],
        { send: true, from: req.userAddress }
      ).catch(error => {
        console.warn('Failed to update last login time:', error);
      });
    }
    next();
  } catch (error) {
    // Don't fail the request if login time update fails
    console.warn('Last login update error:', error);
    next();
  }
};

// Validate address parameter in URL
const validateAddressParam = (paramName = 'address') => {
  return (req, res, next) => {
    const address = req.params[paramName];
    
    if (!address) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: `${paramName} parameter is required`
        }
      });
    }

    if (!blockchainService.isValidAddress(address)) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: `Invalid ${paramName} format`
        }
      });
    }

    req.params[paramName] = blockchainService.toChecksumAddress(address);
    next();
  };
};

// Check if user can access resource (owner or admin)
const requireOwnershipOrAdmin = (getOwnerAddress) => {
  return async (req, res, next) => {
    try {
      if (!req.userAddress || !req.userRole) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'AUTHENTICATION_ERROR',
            message: 'Authentication required'
          }
        });
      }

      // Admin can access everything
      if (req.userRole === 'admin') {
        return next();
      }

      // Get owner address using the provided function
      const ownerAddress = await getOwnerAddress(req);
      
      if (!ownerAddress) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Resource not found'
          }
        });
      }

      // Check if user is the owner
      if (req.userAddress.toLowerCase() !== ownerAddress.toLowerCase()) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'AUTHORIZATION_ERROR',
            message: 'Access denied. You can only access your own resources.'
          }
        });
      }

      next();
    } catch (error) {
      console.error('Ownership check error:', error);
      return res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Authorization check failed'
        }
      });
    }
  };
};

module.exports = {
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  requireRole,
  requireAdmin,
  requireReviewerOrAdmin,
  optionalAuth,
  updateLastLogin,
  validateAddressParam,
  requireOwnershipOrAdmin
};
